{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This example imports functions from the DemoReadSGLXData module to read\n", "# analog data and convert it to volts based on the metadata information.\n", "# The metadata file must be present in the same directory as the binary file.\n", "# Works with both imec and nidq analog channels.\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "from tkinter import Tk\n", "from tkinter import filedialog\n", "from DemoReadSGLXData.readSGLX import readMeta, SampRate, makeMemMapRaw, GainCorrectIM, GainCorrectNI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get bin file from user\n", "root = Tk()         # create the Tkinter widget\n", "root.withdraw()     # hide the <PERSON>kinter root window\n", "\n", "# Windows specific; forces the window to appear in front\n", "root.attributes(\"-topmost\", True)\n", "\n", "binFullPath = Path(filedialog.askopenfilename(title=\"Select binary file\"))\n", "\n", "root.destroy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Other parameters about what data to read\n", "tStart = 0        # in seconds\n", "tEnd = 0.1\n", "chanList = [0]    # list of channels to extract, by index in saved file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["meta = readMeta(binFullPath)\n", "sRate = SampRate(meta)\n", "\n", "firstSamp = int(sRate*tStart)\n", "lastSamp = int(sRate*tEnd)\n", "rawData = makeMemMapRaw(binFullPath, meta)\n", "selectData = rawData[chanList, firstSamp:lastSamp+1]\n", "\n", "\n", "if meta['typeThis'] == 'imec':\n", "    # apply gain correction and convert to uV\n", "    convData = 1e6*GainCorrectIM(selectData, chanList, meta)\n", "else:\n", "    # apply gain correction and convert to mV\n", "    convData = 1e3*GainCorrectNI(selectData, chanList, meta)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot the first of the extracted channels\n", "tDat = np.arange(firstSamp, lastSamp+1, dtype='uint64')\n", "tDat = 1000*tDat/sRate      # plot time axis in msec\n", "fig, ax = plt.subplots()\n", "ax.plot(tDat, convData[0, :])\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}}, "nbformat": 4, "nbformat_minor": 4}