{"specification": "probeinterface", "version": "0.2.18", "probes": [{"ndim": 2, "si_units": "um", "annotations": {"name": "ASSY-156-H1", "manufacturer": "cambridgeneurotech"}, "contact_annotations": {}, "contact_positions": [[277.0, 54.0], [277.0, 45.0], [268.0, 54.0], [259.0, 63.0], [250.0, 9.0], [259.0, 18.0], [259.0, 0.0], [250.0, 27.0], [268.0, 9.0], [277.0, 27.0], [277.0, 0.0], [250.0, 63.0], [259.0, 36.0], [259.0, 54.0], [277.0, 63.0], [268.0, 45.0], [18.0, 18.0], [18.0, 9.0], [9.0, 0.0], [27.0, 27.0], [27.0, 36.0], [0.0, 27.0], [18.0, 45.0], [9.0, 54.0], [9.0, 36.0], [0.0, 63.0], [0.0, 45.0], [18.0, 54.0], [9.0, 18.0], [18.0, 27.0], [250.0, 45.0], [27.0, 9.0], [9.0, 63.0], [18.0, 36.0], [0.0, 54.0], [27.0, 45.0], [18.0, 63.0], [0.0, 36.0], [27.0, 54.0], [9.0, 45.0], [27.0, 63.0], [18.0, 0.0], [9.0, 27.0], [27.0, 0.0], [0.0, 18.0], [0.0, 0.0], [9.0, 9.0], [0.0, 9.0], [259.0, 45.0], [27.0, 18.0], [268.0, 63.0], [250.0, 36.0], [250.0, 54.0], [277.0, 36.0], [268.0, 18.0], [268.0, 36.0], [277.0, 18.0], [259.0, 9.0], [250.0, 18.0], [250.0, 0.0], [259.0, 27.0], [277.0, 9.0], [268.0, 27.0], [268.0, 0.0]], "contact_plane_axes": [[[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]]], "contact_shapes": ["rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect"], "contact_shape_params": [{"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}, {"width": 5, "height": 5}], "probe_planar_contour": [[-10.0, 175.0], [-10.0, 135.0], [-10.0, -10.0], [22.0, -65.0], [49.5, -10.0], [49.5, 135.0], [240.0, 135.0], [240.0, -10.0], [272.0, -65.0], [299.5, -10.0], [299.5, 135.0], [299.5, 175.0]], "contact_ids": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "60", "61", "62", "63", "64"], "shank_ids": ["1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1", "0", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1"]}]}