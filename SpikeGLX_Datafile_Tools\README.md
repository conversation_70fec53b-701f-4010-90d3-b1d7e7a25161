# SpikeGLX_Datafile_Tools

Simple helper functions and data structures demonstrating how to read and manipulate SpikeGLX meta and binary files.

Works with 3A and all current probe types, as of 1/3/24.

The same basic functionality is provided for MATLAB and Python.

The Python material can be used from an interpreter or imported into your own modules. An example Jupyter notebook is included.

The examples are functional and illustrate how to parse the data, but they are not optimized for speed or for memory usage.
