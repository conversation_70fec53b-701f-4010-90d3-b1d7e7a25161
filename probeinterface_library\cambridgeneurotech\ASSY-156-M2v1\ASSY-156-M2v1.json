{"specification": "probeinterface", "version": "0.2.18", "probes": [{"ndim": 2, "si_units": "um", "annotations": {"name": "ASSY-156-M2v1", "manufacturer": "cambridgeneurotech"}, "contact_annotations": {}, "contact_positions": [[0.0, 713.0], [0.0, 837.0], [0.0, 651.0], [0.0, 558.0], [0.0, 248.0], [0.0, 155.0], [0.0, 403.0], [0.0, 0.0], [0.0, 310.0], [0.0, 93.0], [0.0, 465.0], [0.0, 496.0], [0.0, 899.0], [0.0, 589.0], [0.0, 682.0], [0.0, 806.0], [0.0, 1271.0], [0.0, 1147.0], [0.0, 1054.0], [0.0, 1364.0], [0.0, 1488.0], [0.0, 1457.0], [0.0, 1643.0], [0.0, 1860.0], [0.0, 1550.0], [0.0, 1953.0], [0.0, 1705.0], [0.0, 1798.0], [0.0, 1302.0], [0.0, 1395.0], [0.0, 744.0], [0.0, 1116.0], [0.0, 1891.0], [0.0, 1519.0], [0.0, 1922.0], [0.0, 1612.0], [0.0, 1829.0], [0.0, 1581.0], [0.0, 1736.0], [0.0, 1674.0], [0.0, 1767.0], [0.0, 1023.0], [0.0, 1426.0], [0.0, 992.0], [0.0, 1333.0], [0.0, 1085.0], [0.0, 1178.0], [0.0, 1209.0], [0.0, 775.0], [0.0, 1240.0], [0.0, 620.0], [0.0, 868.0], [0.0, 527.0], [0.0, 961.0], [0.0, 186.0], [0.0, 930.0], [0.0, 217.0], [0.0, 279.0], [0.0, 124.0], [0.0, 372.0], [0.0, 31.0], [0.0, 341.0], [0.0, 62.0], [0.0, 434.0]], "contact_plane_axes": [[[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]]], "contact_shapes": ["rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect"], "contact_shape_params": [{"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}], "probe_planar_contour": [[-10.0, 2000.0], [-5.0, -215.0], [136.5, 25.0], [136.5, 2000.0]], "contact_ids": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "60", "61", "62", "63", "64"], "shank_ids": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}]}