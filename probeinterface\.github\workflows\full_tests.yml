name: Test on Ubuntu

on:
  pull_request:
      branches: [main]
      types: [synchronize, opened, reopened]
  workflow_dispatch:
  schedule:
    - cron: "0 12 * * *"  # Daily at noon UTC


jobs:
  build-and-test:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3.11"
    - name: Install package
      run: |
        python -m pip install --upgrade pip
        pip install -e .[test]
    - name: Pytest
      run: |
        pytest --cov=probeinterface --cov-report xml:./coverage.xml
    - uses: codecov/codecov-action@v4
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        fail_ci_if_error: true
        file: ./coverage.xml
