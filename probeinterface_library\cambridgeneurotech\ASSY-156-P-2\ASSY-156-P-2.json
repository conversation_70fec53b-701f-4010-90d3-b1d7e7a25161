{"specification": "probeinterface", "version": "0.2.18", "probes": [{"ndim": 2, "si_units": "um", "annotations": {"name": "ASSY-156-P-2", "manufacturer": "cambridgeneurotech"}, "contact_annotations": {}, "contact_positions": [[522.5, 137.5], [500.0, 50.0], [522.5, 187.5], [500.0, 125.0], [772.5, 112.5], [772.5, 187.5], [750.0, 25.0], [750.0, 175.0], [772.5, 62.5], [750.0, 100.0], [750.0, 0.0], [500.0, 175.0], [500.0, 25.0], [500.0, 100.0], [522.5, 162.5], [522.5, 62.5], [272.5, 162.5], [272.5, 62.5], [250.0, 25.0], [250.0, 100.0], [0.0, 0.0], [250.0, 175.0], [22.5, 62.5], [0.0, 100.0], [0.0, 25.0], [0.0, 175.0], [22.5, 112.5], [22.5, 187.5], [272.5, 187.5], [250.0, 125.0], [522.5, 112.5], [250.0, 50.0], [0.0, 125.0], [22.5, 12.5], [0.0, 150.0], [0.0, 50.0], [0.0, 75.0], [22.5, 37.5], [22.5, 137.5], [22.5, 87.5], [22.5, 162.5], [272.5, 12.5], [250.0, 150.0], [250.0, 0.0], [250.0, 75.0], [272.5, 37.5], [272.5, 87.5], [272.5, 112.5], [522.5, 87.5], [272.5, 137.5], [500.0, 75.0], [522.5, 37.5], [500.0, 150.0], [500.0, 0.0], [772.5, 162.5], [522.5, 12.5], [772.5, 137.5], [772.5, 87.5], [750.0, 75.0], [772.5, 37.5], [750.0, 150.0], [750.0, 50.0], [750.0, 125.0], [772.5, 12.5]], "contact_plane_axes": [[[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]]], "contact_shapes": ["rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect"], "contact_shape_params": [{"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}], "probe_planar_contour": [[-10.0, 270.0], [-10.0, 230.0], [-10.0, -20.0], [35.0, -70.0], [66.5, -25.0], [66.5, 225.0], [240.0, 230.0], [240.0, -20.0], [285.0, -70.0], [316.5, -25.0], [316.5, 225.0], [490.0, 230.0], [490.0, -20.0], [535.0, -70.0], [566.5, -25.0], [566.5, 225.0], [740.0, 230.0], [740.0, -20.0], [785.0, -70.0], [816.5, -25.0], [816.5, 225.0], [816.5, 265.0]], "contact_ids": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "60", "61", "62", "63", "64"], "shank_ids": ["2", "2", "2", "2", "3", "3", "3", "3", "3", "3", "3", "2", "2", "2", "2", "2", "1", "1", "1", "1", "0", "1", "0", "0", "0", "0", "0", "0", "1", "1", "2", "1", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1", "1", "1", "1", "1", "1", "1", "2", "1", "2", "2", "2", "2", "3", "2", "3", "3", "3", "3", "3", "3", "3", "3"]}]}