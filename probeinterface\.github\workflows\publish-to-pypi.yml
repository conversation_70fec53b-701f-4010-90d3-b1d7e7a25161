name: Release to PyPI

on:
  push:
    tags:
       - '*'
jobs:
  release:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3.10"
    - name: Install Tools
      run: |
        python -m pip install --upgrade pip
        pip install setuptools wheel twine build
    - name: Package and Upload
      env:
        STACKMANAGER_VERSION: ${{ github.event.release.tag_name }}
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
      run: |
        python -m build --sdist --wheel
        twine upload dist/*
