{"specification": "probeinterface", "version": "0.2.18", "probes": [{"ndim": 2, "si_units": "um", "annotations": {"name": "ASSY-156-H10", "manufacturer": "cambridgeneurotech"}, "contact_annotations": {}, "contact_positions": [[150.0, 60.0], [168.5, -15.0], [150.0, 120.0], [150.0, 270.0], [187.0, 60.0], [168.5, 105.0], [187.0, 270.0], [168.5, 285.0], [187.0, 120.0], [168.5, 165.0], [187.0, 210.0], [150.0, 210.0], [168.5, 45.0], [150.0, 180.0], [150.0, 90.0], [168.5, -45.0], [37.0, 0.0], [37.0, 120.0], [37.0, 270.0], [18.5, 165.0], [18.5, 255.0], [18.5, 285.0], [18.5, -45.0], [0.0, 180.0], [18.5, 45.0], [0.0, 210.0], [0.0, 30.0], [0.0, 120.0], [18.5, 105.0], [18.5, 195.0], [150.0, 30.0], [37.0, 150.0], [0.0, 270.0], [18.5, 75.0], [18.5, -15.0], [0.0, 240.0], [0.0, 150.0], [18.5, 15.0], [0.0, 60.0], [0.0, 0.0], [0.0, 90.0], [37.0, 240.0], [18.5, 225.0], [37.0, 210.0], [18.5, 135.0], [37.0, 180.0], [37.0, 90.0], [37.0, 60.0], [150.0, 0.0], [37.0, 30.0], [150.0, 150.0], [168.5, 15.0], [150.0, 240.0], [168.5, 255.0], [187.0, 0.0], [168.5, 75.0], [187.0, 30.0], [187.0, 90.0], [168.5, 135.0], [187.0, 180.0], [168.5, 225.0], [187.0, 150.0], [168.5, 195.0], [187.0, 240.0]], "contact_plane_axes": [[[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]]], "contact_shapes": ["rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect"], "contact_shape_params": [{"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}], "probe_planar_contour": [[-10.0, 440.0], [-10.0, 400.0], [-10.0, 0.0], [22.0, -50.0], [32.0, 0.0], [32.0, 400.0], [140.0, 400.0], [140.0, 0.0], [172.0, -50.0], [182.0, 0.0], [182.0, 400.0], [182.0, 440.0]], "contact_ids": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "60", "61", "62", "63", "64"], "shank_ids": ["1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1", "0", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1"]}]}