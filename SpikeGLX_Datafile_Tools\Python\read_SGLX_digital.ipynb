{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This example imports functions from the DemoReadSGLXData module to read\n", "# digital data. The metadata file must be present in the same directory as the binary file.\n", "# Works with both imec and nidq digital channels.\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "from tkinter import Tk\n", "from tkinter import filedialog\n", "from DemoReadSGLXData.readSGLX import readMeta, SampRate, makeMemMapRaw, ExtractDigital"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get file from user\n", "root = Tk()         # create the Tkinter widget\n", "root.withdraw()     # hide the <PERSON>kinter root window\n", "\n", "# Windows specific; forces the window to appear in front\n", "root.attributes(\"-topmost\", True)\n", "\n", "binFullPath = Path(filedialog.askopenfilename(title=\"Select binary file\"))\n", "\n", "root.destroy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Other parameters about what data to read\n", "tStart = 0        # in seconds\n", "tEnd = 1\n", "# Which digital word to read. \n", "# For imec, there is only 1 digital word, dw = 0.\n", "# For NI, digital lines 0-15 are in word 0, lines 16-31 are in word 1, etc.\n", "dw = 0    \n", "# Which lines within the digital word, zero-based\n", "# Note that the SYNC line for PXI 3B is stored in line 6.\n", "dLineList = [0,1,6]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["meta = readMeta(binFullPath)\n", "sRate = SampRate(meta)\n", "\n", "firstSamp = int(sRate*tStart)\n", "lastSamp = int(sRate*tEnd)\n", "rawData = makeMemMapRaw(binFullPath, meta)\n", "\n", "# get digital data for the selected lines\n", "digArray = ExtractDigital(rawData, firstSamp, lastSamp, dw, dLineList, meta)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot the extracted digital channels\n", "tDat = np.arange(firstSamp, lastSamp+1, dtype='uint64')\n", "tDat = 1000*tDat/sRate      # plot time axis in msec\n", "fig, ax = plt.subplots()\n", "for i in range(0, len(dLineList)):\n", "    ax.plot(tDat, digArray[i, :])\n", "plt.show()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}}, "nbformat": 4, "nbformat_minor": 4}