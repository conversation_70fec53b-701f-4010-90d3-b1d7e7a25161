import spikeinterface.extractors as se

binary_file = r"Z:\users\izouridis\test_sorting_npx2\b11\b11_p1_r1_g0\b11_p1_r1_g0_t0.obx0.obx.bin"

recording = se.BinaryRecordingExtractor(
    file_paths=[binary_file],   # note: this expects a list of files
    sampling_frequency=30303,
    num_channels=14,
    dtype='int16',
    time_axis=0
)

print(recording)
print(f"Channels: {recording.get_num_channels()}")
print(f"Sampling frequency: {recording.get_sampling_frequency()} Hz")
print(f"Duration: {recording.get_total_duration():.2f} seconds")

import matplotlib.pyplot as plt
import spikeinterface.preprocessing as spre
import numpy as np

# Slice channel 1
recording_ch1 = recording.channel_slice(channel_ids=[1])

# Filter channel 1 (0.01 Hz high-pass)
filtered_ch1 = spre.highpass_filter(recording_ch1, freq_min=0.002)

# Get traces
raw_trace = recording_ch1.get_traces()[:, 0]
filt_trace = filtered_ch1.get_traces()[:, 0]

# Time axis
fs = recording.get_sampling_frequency()
num_frames = recording.get_num_frames()
time_axis = np.arange(num_frames) / fs

# Plot raw vs. filtered
plt.figure(figsize=(15, 5))
plt.plot(time_axis, raw_trace, label='Raw Channel 1', color='gray', linewidth=0.5)
plt.plot(time_axis, filt_trace, label='Filtered Channel 1 (0.01 Hz HP)', color='green', linewidth=0.5)

plt.xlabel("Time (s)")
plt.ylabel("Amplitude")
plt.title("Raw vs. Filtered (0.01 Hz HP) Channel 1")
plt.legend(loc="upper right")
plt.tight_layout()
plt.show()


import plotly.graph_objects as go

# Slice channel 0
recording_ch0 = recording.channel_slice(channel_ids=[0])

# Get sampling frequency and number of samples for 100 seconds
fs = recording.get_sampling_frequency()
num_samples = int(100 * fs)

# Get first 10 seconds of trace
trace_ch0 = recording_ch0.get_traces(start_frame=0, end_frame=num_samples)[:, 0]
time_axis = np.arange(num_samples) / fs

# Create interactive plot
fig = go.Figure()
fig.add_trace(go.Scatter(
    x=time_axis,
    y=trace_ch0,
    mode='lines',
    line=dict(color='blue', width=1),
    name='Channel 0 (Raw)'
))

fig.update_layout(
    title='Interactive Zoom: Channel 0 (First 100 Seconds)',
    xaxis_title='Time (s)',
    yaxis_title='Amplitude',
    hovermode='x',
    template='plotly_white'
)

fig.show()

