{"specification": "probeinterface", "version": "0.2.18", "probes": [{"ndim": 2, "si_units": "um", "annotations": {"name": "ASSY-156-H6", "manufacturer": "cambridgeneurotech"}, "contact_annotations": {}, "contact_positions": [[250.0, 200.0], [272.5, 362.5], [250.0, 250.0], [250.0, 325.0], [272.5, 87.5], [250.0, 125.0], [250.0, 25.0], [272.5, 237.5], [272.5, 62.5], [272.5, 162.5], [250.0, 0.0], [250.0, 375.0], [272.5, 312.5], [250.0, 300.0], [250.0, 225.0], [272.5, 387.5], [22.5, 112.5], [22.5, 62.5], [0.0, 25.0], [22.5, 162.5], [22.5, 262.5], [22.5, 237.5], [22.5, 387.5], [0.0, 300.0], [22.5, 312.5], [0.0, 375.0], [0.0, 175.0], [0.0, 250.0], [0.0, 125.0], [22.5, 187.5], [250.0, 175.0], [0.0, 50.0], [0.0, 325.0], [22.5, 287.5], [0.0, 350.0], [22.5, 362.5], [0.0, 275.0], [22.5, 337.5], [0.0, 200.0], [0.0, 150.0], [0.0, 225.0], [22.5, 12.5], [22.5, 212.5], [0.0, 0.0], [22.5, 137.5], [22.5, 37.5], [0.0, 75.0], [22.5, 87.5], [250.0, 150.0], [0.0, 100.0], [250.0, 275.0], [272.5, 337.5], [250.0, 350.0], [272.5, 262.5], [272.5, 112.5], [272.5, 287.5], [250.0, 100.0], [250.0, 75.0], [272.5, 137.5], [272.5, 37.5], [272.5, 212.5], [250.0, 50.0], [272.5, 187.5], [272.5, 12.5]], "contact_plane_axes": [[[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]]], "contact_shapes": ["rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect"], "contact_shape_params": [{"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}, {"width": 11, "height": 15}], "probe_planar_contour": [[-10.0, 490.0], [-10.0, 450.0], [-10.0, -25.0], [11.0, -65.0], [32.0, -25.0], [56.5, 450.0], [240.0, 450.0], [240.0, -25.0], [261.0, -65.0], [282.0, -25.0], [306.5, 450.0], [306.5, 490.0]], "contact_ids": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "60", "61", "62", "63", "64"], "shank_ids": ["1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1", "0", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1"]}]}