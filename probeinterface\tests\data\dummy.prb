total_nb_channels = 32
channel_groups = {
     0:
        {
           'channels': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31],
           'graph':  [(0, 1), (0, 2), (0, 3), (0, 10), (0, 11), (0, 12), (0, 13), (0, 14), (0, 22), (0, 23), (0, 24), (0, 25), (1, 2), (1, 3), (1, 4), (1, 10), (1, 11), (1, 12), (1, 13), (1, 14), (1, 15), (1, 22), (1, 23), (1, 24), (1, 25), (1, 26), (2, 3), (2, 4), (2, 5), (2, 10), (2, 11), (2, 12), (2, 13), (2, 14), (2, 15), (2, 16), (2, 22), (2, 23), (2, 24), (2, 25), (2, 26), (2, 27), (3, 4), (3, 5), (3, 6), (3, 10), (3, 11), (3, 12), (3, 13), (3, 14), (3, 15), (3, 16), (3, 17), (3, 22), (3, 23), (3, 24), (3, 25), (3, 26), (3, 27), (3, 28), (4, 5), (4, 6), (4, 7), (4, 11), (4, 12), (4, 13), (4, 14), (4, 15), (4, 16), (4, 17), (4, 18), (4, 23), (4, 24), (4, 25), (4, 26), (4, 27), (4, 28), (4, 29), (5, 6), (5, 7), (5, 8), (5, 12), (5, 13), (5, 14), (5, 15), (5, 16), (5, 17), (5, 18), (5, 19), (5, 24), (5, 25), (5, 26), (5, 27), (5, 28), (5, 29), (5, 30), (6, 7), (6, 8), (6, 9), (6, 13), (6, 14), (6, 15), (6, 16), (6, 17), (6, 18), (6, 19), (6, 20), (6, 25), (6, 26), (6, 27), (6, 28), (6, 29), (6, 30), (6, 31), (7, 8), (7, 9), (7, 14), (7, 15), (7, 16), (7, 17), (7, 18), (7, 19), (7, 20), (7, 21), (7, 26), (7, 27), (7, 28), (7, 29), (7, 30), (7, 31), (8, 9), (8, 15), (8, 16), (8, 17), (8, 18), (8, 19), (8, 20), (8, 21), (8, 27), (8, 28), (8, 29), (8, 30), (8, 31), (9, 16), (9, 17), (9, 18), (9, 19), (9, 20), (9, 21), (9, 28), (9, 29), (9, 30), (9, 31), (10, 11), (10, 12), (10, 13), (10, 22), (10, 23), (10, 24), (10, 25), (11, 12), (11, 13), (11, 14), (11, 22), (11, 23), (11, 24), (11, 25), (11, 26), (12, 13), (12, 14), (12, 15), (12, 22), (12, 23), (12, 24), (12, 25), (12, 26), (12, 27), (13, 14), (13, 15), (13, 16), (13, 22), (13, 23), (13, 24), (13, 25), (13, 26), (13, 27), (13, 28), (14, 15), (14, 16), (14, 17), (14, 22), (14, 23), (14, 24), (14, 25), (14, 26), (14, 27), (14, 28), (14, 29), (15, 16), (15, 17), (15, 18), (15, 23), (15, 24), (15, 25), (15, 26), (15, 27), (15, 28), (15, 29), (15, 30), (16, 17), (16, 18), (16, 19), (16, 24), (16, 25), (16, 26), (16, 27), (16, 28), (16, 29), (16, 30), (16, 31), (17, 18), (17, 19), (17, 20), (17, 25), (17, 26), (17, 27), (17, 28), (17, 29), (17, 30), (17, 31), (18, 19), (18, 20), (18, 21), (18, 26), (18, 27), (18, 28), (18, 29), (18, 30), (18, 31), (19, 20), (19, 21), (19, 27), (19, 28), (19, 29), (19, 30), (19, 31), (20, 21), (20, 28), (20, 29), (20, 30), (20, 31), (21, 29), (21, 30), (21, 31), (22, 23), (22, 24), (22, 25), (23, 24), (23, 25), (23, 26), (24, 25), (24, 26), (24, 27), (25, 26), (25, 27), (25, 28), (26, 27), (26, 28), (26, 29), (27, 28), (27, 29), (27, 30), (28, 29), (28, 30), (28, 31), (29, 30), (29, 31), (30, 31)],
           'geometry':  {
               0: [-18.0, -117.1875],
               1: [-18.0, -92.1875],
               2: [-18.0, -67.1875],
               3: [-18.0, -42.1875],
               4: [-18.0, -17.1875],
               5: [-18.0, 7.8125],
               6: [-18.0, 32.8125],
               7: [-18.0, 57.8125],
               8: [-18.0, 82.8125],
               9: [-18.0, 107.8125],
               10: [0.0, -129.6875],
               11: [0.0, -104.6875],
               12: [0.0, -79.6875],
               13: [0.0, -54.6875],
               14: [0.0, -29.6875],
               15: [0.0, -4.6875],
               16: [0.0, 20.3125],
               17: [0.0, 45.3125],
               18: [0.0, 70.3125],
               19: [0.0, 95.3125],
               20: [0.0, 120.3125],
               21: [0.0, 145.3125],
               22: [18.0, -117.1875],
               23: [18.0, -92.1875],
               24: [18.0, -67.1875],
               25: [18.0, -42.1875],
               26: [18.0, -17.1875],
               27: [18.0, 7.8125],
               28: [18.0, 32.8125],
               29: [18.0, 57.8125],
               30: [18.0, 82.8125],
               31: [18.0, 107.8125],
           }
       },
}
